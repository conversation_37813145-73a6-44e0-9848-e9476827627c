# ELD Trip Planner Backend

A Django REST Framework backend for Electronic Logging Device (ELD) trip planning and compliance management.

## Features

- **User Authentication**: JWT-based authentication with custom User model
- **Trip Management**: Create, manage, and track trips with multiple waypoints
- **ELD Compliance**: Automatic calculation of Hours of Service (HOS) compliance
- **Route Computation**: Integration with OSRM and Mapbox for route calculation
- **Fuel Stop Management**: Automatic insertion of mandatory fuel stops
- **Data Export**: CSV export functionality for trips and ELD logs
- **Admin Interface**: Django admin for data management

## Technology Stack

- **Framework**: Django 4.2.7 with Django REST Framework
- **Database**: PostgreSQL (production) / SQLite (development)
- **Authentication**: JWT tokens with Simple JWT
- **API Documentation**: drf-spectacular (OpenAPI/Swagger)
- **Caching**: Redis (optional)
- **Deployment**: Render-ready with WhiteNoise for static files

## Quick Start

### Prerequisites

- Python 3.9+
- pip and virtualenv
- PostgreSQL (for production)

### Installation

1. **Clone and navigate to backend directory**:
   ```bash
   cd backend
   ```

2. **Create and activate virtual environment**:
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables** (create `.env` file):
   ```env
   DJANGO_SECRET_KEY=your-secret-key-here
   DEBUG=True
   DATABASE_URL=sqlite:///db.sqlite3  # For development
   ALLOWED_HOSTS=localhost,127.0.0.1
   CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
   
   # Optional: Mapping providers
   MAPBOX_ACCESS_TOKEN=your-mapbox-token
   OSRM_BASE_URL=http://router.project-osrm.org
   
   # Optional: Redis caching
   REDIS_URL=redis://localhost:6379/0
   ```

5. **Run database migrations**:
   ```bash
   python manage.py migrate
   ```

6. **Create superuser**:
   ```bash
   python manage.py createsuperuser
   ```

7. **Start development server**:
   ```bash
   python manage.py runserver
   ```

The API will be available at `http://localhost:8000/api/`

## API Endpoints

### Authentication
- `POST /api/auth/register/` - User registration
- `POST /api/auth/login/` - User login
- `POST /api/auth/token/refresh/` - Refresh JWT token
- `GET /api/auth/profile/` - Get user profile
- `PUT /api/auth/profile/update/` - Update user profile

### Vehicles
- `GET /api/auth/vehicles/` - List vehicles
- `POST /api/auth/vehicles/` - Create vehicle
- `GET /api/auth/vehicles/{id}/` - Get vehicle details
- `PUT /api/auth/vehicles/{id}/` - Update vehicle
- `DELETE /api/auth/vehicles/{id}/` - Delete vehicle

### Trips
- `GET /api/trips/` - List trips
- `POST /api/trips/` - Create trip
- `GET /api/trips/{id}/` - Get trip details
- `PUT /api/trips/{id}/` - Update trip
- `DELETE /api/trips/{id}/` - Delete trip
- `POST /api/trips/{id}/calculate_compliance/` - Calculate ELD compliance
- `POST /api/trips/{id}/compute_route/` - Compute route for trip
- `POST /api/trips/bulk_delete/` - Bulk delete trips

### ELD Logs
- `GET /api/eld-logs/` - List ELD logs
- `POST /api/eld-logs/` - Create ELD log
- `GET /api/eld-logs/{id}/` - Get ELD log details
- `PUT /api/eld-logs/{id}/` - Update ELD log
- `DELETE /api/eld-logs/{id}/` - Delete ELD log
- `GET /api/eld-logs/cycle_compliance/` - Get 70-hour cycle compliance

### Routing & Utilities
- `POST /api/compute-route/` - Compute route for waypoints
- `POST /api/geocode/` - Geocode address to coordinates
- `GET /api/providers/` - List available mapping providers
- `POST /api/export/` - Export trip data (CSV)
- `GET /api/health/` - Health check endpoint

### API Documentation
- `GET /api/docs/` - Swagger UI documentation
- `GET /api/schema/` - OpenAPI schema

## ELD Compliance Features

### Hours of Service Regulations
- **70-hour 8-day cycle limit**: Tracks cumulative on-duty time
- **11-hour daily driving limit**: Monitors daily driving time
- **14-hour daily on-duty limit**: Tracks total on-duty time per day
- **10-hour mandatory off-duty period**: Ensures adequate rest

### Automatic Calculations
- **Duty Status Segmentation**: Automatically calculates driving, on-duty, and off-duty periods
- **Violation Detection**: Identifies HOS violations with detailed messages
- **Fuel Stop Insertion**: Adds mandatory fuel stops every 1,000 miles
- **Timezone Handling**: Proper timezone-aware datetime calculations

## Testing

Run the test suite:

```bash
# Run all tests
python manage.py test

# Run specific test class
python manage.py test trips.tests.ELDCalculatorTestCase

# Run with verbose output
python manage.py test -v 2
```

## Deployment

### Environment Variables for Production

```env
DJANGO_SECRET_KEY=your-production-secret-key
DEBUG=False
DATABASE_URL=postgresql://user:password@host:port/database
ALLOWED_HOSTS=your-domain.com,api.your-domain.com
CORS_ALLOWED_ORIGINS=https://your-frontend-domain.com

# Optional services
MAPBOX_ACCESS_TOKEN=your-mapbox-token
REDIS_URL=redis://your-redis-host:6379/0
```

### Render Deployment

1. Connect your repository to Render
2. Set environment variables in Render dashboard
3. Use the following build command:
   ```bash
   pip install -r requirements.txt && python manage.py collectstatic --noinput && python manage.py migrate
   ```
4. Start command: `gunicorn eld_trip_planner.wsgi:application`

## Project Structure

```
backend/
├── eld_trip_planner/          # Main project settings
│   ├── settings.py            # Django settings with environment config
│   ├── urls.py               # Main URL configuration
│   └── wsgi.py               # WSGI application
├── accounts/                  # User authentication and vehicles
│   ├── models.py             # User and Vehicle models
│   ├── serializers.py        # API serializers
│   ├── views.py              # Authentication views
│   └── urls.py               # Authentication URLs
├── trips/                     # Trip management and ELD compliance
│   ├── models.py             # Trip, Waypoint, ELD log models
│   ├── serializers.py        # Trip and ELD serializers
│   ├── views.py              # Trip management views
│   ├── eld_calculator.py     # ELD compliance calculation engine
│   ├── urls.py               # Trip management URLs
│   └── tests.py              # Test cases
├── routing/                   # Routing and mapping services
│   └── services.py           # OSRM/Mapbox integration
├── requirements.txt           # Python dependencies
└── README.md                 # This file
```

## Contributing

1. Follow PEP 8 style guidelines
2. Write tests for new features
3. Update documentation for API changes
4. Use meaningful commit messages

## License

This project is part of the ELD Trip Planning Application assessment.
