"""
Routing Services

This module provides integration with mapping providers for route computation
and geocoding services.
"""

import requests
import logging
from typing import List, Dict, Any, Optional, Tuple
from django.conf import settings
from django.core.cache import cache
from geopy.distance import geodesic

logger = logging.getLogger(__name__)


class RoutingService:
    """
    Service for route computation using various mapping providers.
    """
    
    def __init__(self):
        self.mapbox_token = settings.MAPBOX_ACCESS_TOKEN
        self.osrm_base_url = settings.OSRM_BASE_URL
        self.nominatim_base_url = settings.NOMINATIM_BASE_URL

    def compute_route(self, waypoints: List[Dict[str, Any]], provider: str = 'osrm') -> Dict[str, Any]:
        """
        Compute route between multiple waypoints.
        
        Args:
            waypoints: List of waypoint dictionaries with lat/lng
            provider: Routing provider ('osrm' or 'mapbox')
            
        Returns:
            Dictionary with route information including distance, duration, and geometry
        """
        if provider == 'mapbox' and self.mapbox_token:
            return self._compute_route_mapbox(waypoints)
        else:
            return self._compute_route_osrm(waypoints)

    def _compute_route_osrm(self, waypoints: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Compute route using OSRM (Open Source Routing Machine).
        """
        try:
            # Build coordinates string for OSRM
            coordinates = []
            for waypoint in waypoints:
                coordinates.append(f"{waypoint['longitude']},{waypoint['latitude']}")
            
            coordinates_str = ';'.join(coordinates)
            
            # OSRM API call
            url = f"{self.osrm_base_url}/route/v1/driving/{coordinates_str}"
            params = {
                'overview': 'full',
                'geometries': 'geojson',
                'steps': 'true'
            }
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if data['code'] != 'Ok':
                raise Exception(f"OSRM error: {data.get('message', 'Unknown error')}")
            
            route = data['routes'][0]
            
            return {
                'provider': 'osrm',
                'distance_meters': route['distance'],
                'distance_miles': route['distance'] * 0.*********,  # Convert to miles
                'duration_seconds': route['duration'],
                'duration_minutes': route['duration'] / 60,
                'geometry': route['geometry'],
                'legs': route['legs'],
                'waypoints': data['waypoints']
            }
            
        except Exception as e:
            logger.error(f"OSRM routing error: {str(e)}")
            # Fallback to simple distance calculation
            return self._fallback_route_calculation(waypoints)

    def _compute_route_mapbox(self, waypoints: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Compute route using Mapbox Directions API.
        """
        try:
            # Build coordinates string for Mapbox
            coordinates = []
            for waypoint in waypoints:
                coordinates.append(f"{waypoint['longitude']},{waypoint['latitude']}")
            
            coordinates_str = ';'.join(coordinates)
            
            # Mapbox API call
            url = f"https://api.mapbox.com/directions/v5/mapbox/driving/{coordinates_str}"
            params = {
                'access_token': self.mapbox_token,
                'overview': 'full',
                'geometries': 'geojson',
                'steps': 'true'
            }
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if data['code'] != 'Ok':
                raise Exception(f"Mapbox error: {data.get('message', 'Unknown error')}")
            
            route = data['routes'][0]
            
            return {
                'provider': 'mapbox',
                'distance_meters': route['distance'],
                'distance_miles': route['distance'] * 0.*********,  # Convert to miles
                'duration_seconds': route['duration'],
                'duration_minutes': route['duration'] / 60,
                'geometry': route['geometry'],
                'legs': route['legs'],
                'waypoints': data['waypoints']
            }
            
        except Exception as e:
            logger.error(f"Mapbox routing error: {str(e)}")
            # Fallback to simple distance calculation
            return self._fallback_route_calculation(waypoints)

    def _fallback_route_calculation(self, waypoints: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Fallback route calculation using simple distance calculations.
        """
        total_distance_miles = 0
        total_duration_minutes = 0
        
        for i in range(len(waypoints) - 1):
            current = waypoints[i]
            next_point = waypoints[i + 1]
            
            # Calculate distance using geodesic
            distance = geodesic(
                (current['latitude'], current['longitude']),
                (next_point['latitude'], next_point['longitude'])
            ).miles
            
            total_distance_miles += distance
            
            # Estimate duration (assuming 55 mph average speed)
            duration_hours = distance / 55
            total_duration_minutes += duration_hours * 60
        
        return {
            'provider': 'fallback',
            'distance_meters': total_distance_miles * 1609.34,  # Convert to meters
            'distance_miles': total_distance_miles,
            'duration_seconds': total_duration_minutes * 60,
            'duration_minutes': total_duration_minutes,
            'geometry': None,  # No geometry available in fallback
            'legs': [],
            'waypoints': waypoints
        }

    def geocode_address(self, address: str) -> Optional[Dict[str, Any]]:
        """
        Geocode an address to get latitude and longitude.
        
        Args:
            address: Address string to geocode
            
        Returns:
            Dictionary with geocoding results or None if not found
        """
        # Check cache first
        cache_key = f"geocode:{address}"
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
        
        try:
            # Use Nominatim for geocoding (free service)
            url = f"{self.nominatim_base_url}/search"
            params = {
                'q': address,
                'format': 'json',
                'limit': 1,
                'addressdetails': 1
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                return None
            
            result = data[0]
            geocoded = {
                'address': result.get('display_name', address),
                'latitude': float(result['lat']),
                'longitude': float(result['lon']),
                'place_id': result.get('place_id'),
                'importance': result.get('importance'),
                'address_components': result.get('address', {})
            }
            
            # Cache the result for 24 hours
            cache.set(cache_key, geocoded, 86400)
            
            return geocoded
            
        except Exception as e:
            logger.error(f"Geocoding error for address '{address}': {str(e)}")
            return None

    def reverse_geocode(self, latitude: float, longitude: float) -> Optional[Dict[str, Any]]:
        """
        Reverse geocode coordinates to get address.
        
        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            
        Returns:
            Dictionary with reverse geocoding results or None if not found
        """
        # Check cache first
        cache_key = f"reverse_geocode:{latitude},{longitude}"
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
        
        try:
            # Use Nominatim for reverse geocoding
            url = f"{self.nominatim_base_url}/reverse"
            params = {
                'lat': latitude,
                'lon': longitude,
                'format': 'json',
                'addressdetails': 1
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if 'error' in data:
                return None
            
            result = {
                'address': data.get('display_name'),
                'latitude': latitude,
                'longitude': longitude,
                'place_id': data.get('place_id'),
                'address_components': data.get('address', {})
            }
            
            # Cache the result for 24 hours
            cache.set(cache_key, result, 86400)
            
            return result
            
        except Exception as e:
            logger.error(f"Reverse geocoding error for {latitude},{longitude}: {str(e)}")
            return None

    def get_available_providers(self) -> List[Dict[str, Any]]:
        """
        Get list of available routing providers and their status.
        
        Returns:
            List of provider dictionaries with status information
        """
        providers = []
        
        # Check OSRM availability
        try:
            response = requests.get(f"{self.osrm_base_url}/route/v1/driving/0,0;1,1", timeout=5)
            osrm_available = response.status_code in [200, 400]  # 400 is expected for invalid coords
        except:
            osrm_available = False
        
        providers.append({
            'name': 'osrm',
            'display_name': 'Open Source Routing Machine',
            'available': osrm_available,
            'base_url': self.osrm_base_url
        })
        
        # Check Mapbox availability
        mapbox_available = bool(self.mapbox_token)
        if mapbox_available:
            try:
                # Test with a simple request
                url = "https://api.mapbox.com/directions/v5/mapbox/driving/0,0;1,1"
                params = {'access_token': self.mapbox_token}
                response = requests.get(url, params=params, timeout=5)
                mapbox_available = response.status_code in [200, 422]  # 422 is expected for invalid coords
            except:
                mapbox_available = False
        
        providers.append({
            'name': 'mapbox',
            'display_name': 'Mapbox Directions',
            'available': mapbox_available,
            'requires_token': True,
            'token_configured': bool(self.mapbox_token)
        })
        
        return providers
