# Django Configuration
DJANGO_SECRET_KEY=django-insecure-change-this-in-production
DEBUG=True
DJANGO_LOG_LEVEL=INFO

# Database Configuration
# For development (SQLite)
# DATABASE_URL=sqlite:///db.sqlite3

# For production (PostgreSQL)
# DATABASE_URL=postgresql://username:password@host:port/database_name

# Allowed Hosts (comma-separated)
ALLOWED_HOSTS=localhost,127.0.0.1

# CORS Configuration (comma-separated)
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Mapping Provider Configuration
# Mapbox (optional - for enhanced routing)
MAPBOX_ACCESS_TOKEN=

# OSRM (optional - defaults to public instance)
OSRM_BASE_URL=http://router.project-osrm.org

# Nominatim (optional - defaults to public instance)
NOMINATIM_BASE_URL=https://nominatim.openstreetmap.org

# Redis Configuration (optional - for caching)
REDIS_URL=

# Example production values:
# DJANGO_SECRET_KEY=your-super-secret-production-key-here
# DEBUG=False
# DATABASE_URL=postgresql://eld_user:secure_password@localhost:5432/eld_trip_planner
# ALLOWED_HOSTS=api.yourdomain.com,yourdomain.com
# CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
# MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoieW91cnVzZXJuYW1lIiwiYSI6ImNsb25nLXRva2VuLWhlcmUifQ.signature
# REDIS_URL=redis://localhost:6379/0
