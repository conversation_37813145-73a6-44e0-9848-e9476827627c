from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from .models import Trip, TripWaypoint, ELDLog, LogEntry
from .eld_calculator import ELDComplianceCalculator
from accounts.models import Vehicle

User = get_user_model()


class ELDCalculatorTestCase(TestCase):
    """
    Test cases for ELD compliance calculations.
    """

    def setUp(self):
        """
        Set up test data.
        """
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            name='Test User',
            password='testpass123'
        )

        self.vehicle = Vehicle.objects.create(
            name='Test Truck',
            unit_number='T001',
            odometer_start=100000
        )

        self.calculator = ELDComplianceCalculator()

        # Sample waypoints for testing
        self.sample_waypoints = [
            {
                'sequence_order': 1,
                'waypoint_type': 'pickup',
                'address': 'Chicago, IL',
                'latitude': 41.8781,
                'longitude': -87.6298,
                'duration_minutes': 60
            },
            {
                'sequence_order': 2,
                'waypoint_type': 'dropoff',
                'address': 'Detroit, MI',
                'latitude': 42.3314,
                'longitude': -83.0458,
                'duration_minutes': 60
            }
        ]

    def test_daily_driving_limit_calculation(self):
        """
        Test daily driving limit compliance (11 hours).
        """
        start_time = timezone.now().replace(hour=8, minute=0, second=0, microsecond=0)

        trip_data = {
            'waypoints': self.sample_waypoints,
            'start_time': start_time
        }

        result = self.calculator.calculate_trip_compliance(trip_data)

        # Should have duty segments
        self.assertIn('duty_segments', result)
        self.assertIn('violations', result)
        self.assertIn('totals', result)

        # Check that driving segments are calculated
        driving_segments = [s for s in result['duty_segments']
                            if s['duty_status'] == 'driving']
        self.assertGreater(len(driving_segments), 0)

    def test_fuel_stop_insertion(self):
        """
        Test automatic fuel stop insertion for long trips.
        """
        # Create waypoints for a 1500-mile trip (should trigger fuel stop)
        long_waypoints = [
            {
                'sequence_order': 1,
                'waypoint_type': 'pickup',
                'address': 'Los Angeles, CA',
                'latitude': 34.0522,
                'longitude': -118.2437,
                'duration_minutes': 60
            },
            {
                'sequence_order': 2,
                'waypoint_type': 'dropoff',
                'address': 'New York, NY',
                'latitude': 40.7128,
                'longitude': -74.0060,
                'duration_minutes': 60
            }
        ]

        total_distance = 2500  # Miles - should trigger fuel stops

        result = self.calculator.insert_fuel_stops(
            long_waypoints, total_distance)

        # Should have added fuel stops
        self.assertGreater(len(result), len(long_waypoints))

        # Check for fuel stop waypoints
        fuel_stops = [w for w in result if w.get('waypoint_type') == 'fuel']
        self.assertGreater(len(fuel_stops), 0)

    def test_cycle_compliance_calculation(self):
        """
        Test 70-hour 8-day cycle compliance calculation.
        """
        # Create some ELD logs for testing
        base_date = timezone.now().date()

        for i in range(7):  # 7 days of logs
            log_date = base_date - timedelta(days=i)
            ELDLog.objects.create(
                user=self.user,
                log_date=log_date,
                total_on_duty_time_minutes=600,  # 10 hours per day
                total_drive_time_minutes=480,    # 8 hours driving
                total_off_duty_time_minutes=840  # 14 hours off duty
            )

        result = self.calculator.calculate_cycle_compliance(
            str(self.user.id),
            timezone.now()
        )

        self.assertIn('total_on_duty_hours', result)
        self.assertIn('remaining_hours', result)
        self.assertIn('is_compliant', result)
        self.assertIn('daily_logs', result)

        # 7 days * 10 hours = 70 hours total
        self.assertEqual(result['total_on_duty_hours'], 70.0)
        self.assertEqual(result['remaining_hours'], 0.0)
        self.assertTrue(result['is_compliant'])
