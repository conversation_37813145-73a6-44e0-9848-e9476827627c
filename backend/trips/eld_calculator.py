"""
ELD Compliance Calculator

This module implements the core ELD compliance calculation logic according to
FMCSA Hours of Service regulations.
"""

from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Tuple
from django.conf import settings
from django.utils import timezone as django_timezone
import pytz


class ELDComplianceCalculator:
    """
    Calculator for ELD compliance based on FMCSA Hours of Service regulations.
    """
    
    def __init__(self):
        self.eld_settings = settings.ELD_SETTINGS
        self.cycle_limit_hours = self.eld_settings['CYCLE_LIMIT_HOURS']
        self.cycle_period_days = self.eld_settings['CYCLE_PERIOD_DAYS']
        self.daily_driving_limit_hours = self.eld_settings['DAILY_DRIVING_LIMIT_HOURS']
        self.daily_on_duty_limit_hours = self.eld_settings['DAILY_ON_DUTY_LIMIT_HOURS']
        self.mandatory_off_duty_hours = self.eld_settings['MANDATORY_OFF_DUTY_HOURS']
        self.fuel_stop_interval_miles = self.eld_settings['FUEL_STOP_INTERVAL_MILES']
        self.pickup_dropoff_duration_minutes = self.eld_settings['PICKUP_DROPOFF_DURATION_MINUTES']

    def calculate_trip_compliance(self, trip_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate ELD compliance for a complete trip.
        
        Args:
            trip_data: Dictionary containing trip information including waypoints
            
        Returns:
            Dictionary with compliance analysis and violations
        """
        waypoints = trip_data.get('waypoints', [])
        start_time = trip_data.get('start_time')
        
        if not waypoints or not start_time:
            return {'violations': ['Missing waypoints or start time']}
        
        # Calculate driving segments and duty status
        duty_segments = self._calculate_duty_segments(waypoints, start_time)
        
        # Check for violations
        violations = []
        violations.extend(self._check_daily_driving_limits(duty_segments))
        violations.extend(self._check_daily_on_duty_limits(duty_segments))
        violations.extend(self._check_mandatory_rest_periods(duty_segments))
        
        # Calculate totals
        totals = self._calculate_totals(duty_segments)
        
        return {
            'duty_segments': duty_segments,
            'violations': violations,
            'totals': totals,
            'compliance_status': 'compliant' if not violations else 'violations_found'
        }

    def calculate_cycle_compliance(self, user_id: str, date: datetime) -> Dict[str, Any]:
        """
        Calculate 70-hour 8-day cycle compliance for a user.
        
        Args:
            user_id: User ID to check compliance for
            date: Date to check compliance from
            
        Returns:
            Dictionary with cycle compliance information
        """
        from .models import ELDLog
        
        # Get logs for the past 8 days
        end_date = date.date()
        start_date = end_date - timedelta(days=self.cycle_period_days - 1)
        
        logs = ELDLog.objects.filter(
            user_id=user_id,
            log_date__range=[start_date, end_date]
        ).order_by('log_date')
        
        total_on_duty_minutes = sum(log.total_on_duty_time_minutes for log in logs)
        total_on_duty_hours = total_on_duty_minutes / 60
        
        remaining_hours = max(0, self.cycle_limit_hours - total_on_duty_hours)
        
        return {
            'cycle_start_date': start_date,
            'cycle_end_date': end_date,
            'total_on_duty_hours': total_on_duty_hours,
            'cycle_limit_hours': self.cycle_limit_hours,
            'remaining_hours': remaining_hours,
            'is_compliant': total_on_duty_hours <= self.cycle_limit_hours,
            'daily_logs': [
                {
                    'date': log.log_date,
                    'on_duty_hours': log.total_on_duty_time_minutes / 60,
                    'drive_hours': log.total_drive_time_minutes / 60,
                }
                for log in logs
            ]
        }

    def insert_fuel_stops(self, waypoints: List[Dict[str, Any]], total_distance: float) -> List[Dict[str, Any]]:
        """
        Insert mandatory fuel stops every 1,000 miles.
        
        Args:
            waypoints: List of waypoint dictionaries
            total_distance: Total trip distance in miles
            
        Returns:
            Updated waypoints list with fuel stops inserted
        """
        if total_distance <= self.fuel_stop_interval_miles:
            return waypoints
        
        # Calculate number of fuel stops needed
        fuel_stops_needed = int(total_distance // self.fuel_stop_interval_miles)
        
        # Insert fuel stops at appropriate intervals
        updated_waypoints = []
        distance_per_segment = total_distance / (len(waypoints) - 1)
        current_distance = 0
        fuel_stop_counter = 1
        
        for i, waypoint in enumerate(waypoints):
            updated_waypoints.append(waypoint)
            
            if i < len(waypoints) - 1:  # Not the last waypoint
                current_distance += distance_per_segment
                
                # Check if we need a fuel stop
                if (current_distance >= fuel_stop_counter * self.fuel_stop_interval_miles and 
                    fuel_stop_counter <= fuel_stops_needed):
                    
                    fuel_stop = {
                        'waypoint_type': 'fuel',
                        'address': f'Fuel Stop #{fuel_stop_counter}',
                        'latitude': waypoint['latitude'],  # Approximate location
                        'longitude': waypoint['longitude'],
                        'duration_minutes': 30,  # Standard fuel stop duration
                        'sequence_order': len(updated_waypoints)
                    }
                    updated_waypoints.append(fuel_stop)
                    fuel_stop_counter += 1
        
        # Reorder sequence numbers
        for i, waypoint in enumerate(updated_waypoints):
            waypoint['sequence_order'] = i + 1
        
        return updated_waypoints

    def _calculate_duty_segments(self, waypoints: List[Dict[str, Any]], start_time: datetime) -> List[Dict[str, Any]]:
        """
        Calculate duty status segments based on waypoints and timing.
        """
        segments = []
        current_time = start_time
        
        for i, waypoint in enumerate(waypoints):
            if i == 0:
                # First waypoint - start driving
                segments.append({
                    'duty_status': 'driving',
                    'start_time': current_time,
                    'location': waypoint['address'],
                    'waypoint_type': waypoint.get('waypoint_type', 'pickup')
                })
            else:
                # Calculate driving time to this waypoint
                prev_waypoint = waypoints[i - 1]
                driving_duration = self._estimate_driving_time(prev_waypoint, waypoint)
                
                # End previous driving segment
                segments[-1]['end_time'] = current_time + timedelta(minutes=driving_duration)
                segments[-1]['duration_minutes'] = driving_duration
                
                current_time = segments[-1]['end_time']
                
                # Add on-duty not driving segment for pickup/dropoff
                if waypoint.get('waypoint_type') in ['pickup', 'dropoff']:
                    duration = waypoint.get('duration_minutes', self.pickup_dropoff_duration_minutes)
                    segments.append({
                        'duty_status': 'on_duty_not_driving',
                        'start_time': current_time,
                        'end_time': current_time + timedelta(minutes=duration),
                        'duration_minutes': duration,
                        'location': waypoint['address'],
                        'waypoint_type': waypoint.get('waypoint_type')
                    })
                    current_time += timedelta(minutes=duration)
                
                # Start next driving segment if not the last waypoint
                if i < len(waypoints) - 1:
                    segments.append({
                        'duty_status': 'driving',
                        'start_time': current_time,
                        'location': waypoint['address'],
                        'waypoint_type': waypoint.get('waypoint_type')
                    })
        
        return segments

    def _estimate_driving_time(self, from_waypoint: Dict[str, Any], to_waypoint: Dict[str, Any]) -> int:
        """
        Estimate driving time between two waypoints in minutes.
        This is a simplified calculation - in production, use actual routing data.
        """
        # Simple distance calculation (Haversine formula would be more accurate)
        lat_diff = abs(float(to_waypoint['latitude']) - float(from_waypoint['latitude']))
        lon_diff = abs(float(to_waypoint['longitude']) - float(from_waypoint['longitude']))
        
        # Rough estimate: 1 degree ≈ 69 miles, average speed 55 mph
        distance_miles = ((lat_diff ** 2 + lon_diff ** 2) ** 0.5) * 69
        driving_time_hours = distance_miles / 55  # 55 mph average
        
        return max(15, int(driving_time_hours * 60))  # Minimum 15 minutes

    def _check_daily_driving_limits(self, duty_segments: List[Dict[str, Any]]) -> List[str]:
        """
        Check for daily driving limit violations (11 hours).
        """
        violations = []
        daily_driving = {}
        
        for segment in duty_segments:
            if segment['duty_status'] == 'driving' and 'end_time' in segment:
                date = segment['start_time'].date()
                if date not in daily_driving:
                    daily_driving[date] = 0
                daily_driving[date] += segment['duration_minutes']
        
        for date, minutes in daily_driving.items():
            hours = minutes / 60
            if hours > self.daily_driving_limit_hours:
                violations.append(
                    f"Daily driving limit exceeded on {date}: {hours:.1f} hours "
                    f"(limit: {self.daily_driving_limit_hours} hours)"
                )
        
        return violations

    def _check_daily_on_duty_limits(self, duty_segments: List[Dict[str, Any]]) -> List[str]:
        """
        Check for daily on-duty limit violations (14 hours).
        """
        violations = []
        daily_on_duty = {}
        
        for segment in duty_segments:
            if segment['duty_status'] in ['driving', 'on_duty_not_driving'] and 'end_time' in segment:
                date = segment['start_time'].date()
                if date not in daily_on_duty:
                    daily_on_duty[date] = 0
                daily_on_duty[date] += segment['duration_minutes']
        
        for date, minutes in daily_on_duty.items():
            hours = minutes / 60
            if hours > self.daily_on_duty_limit_hours:
                violations.append(
                    f"Daily on-duty limit exceeded on {date}: {hours:.1f} hours "
                    f"(limit: {self.daily_on_duty_limit_hours} hours)"
                )
        
        return violations

    def _check_mandatory_rest_periods(self, duty_segments: List[Dict[str, Any]]) -> List[str]:
        """
        Check for mandatory 10-hour off-duty rest period violations.
        """
        violations = []
        
        # This is a simplified check - full implementation would track
        # consecutive off-duty periods and ensure 10-hour minimums
        
        return violations

    def _calculate_totals(self, duty_segments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate total times for different duty statuses.
        """
        totals = {
            'total_driving_minutes': 0,
            'total_on_duty_minutes': 0,
            'total_off_duty_minutes': 0,
        }
        
        for segment in duty_segments:
            if 'duration_minutes' in segment:
                if segment['duty_status'] == 'driving':
                    totals['total_driving_minutes'] += segment['duration_minutes']
                    totals['total_on_duty_minutes'] += segment['duration_minutes']
                elif segment['duty_status'] == 'on_duty_not_driving':
                    totals['total_on_duty_minutes'] += segment['duration_minutes']
                elif segment['duty_status'] == 'off_duty':
                    totals['total_off_duty_minutes'] += segment['duration_minutes']
        
        # Convert to hours for easier reading
        totals.update({
            'total_driving_hours': totals['total_driving_minutes'] / 60,
            'total_on_duty_hours': totals['total_on_duty_minutes'] / 60,
            'total_off_duty_hours': totals['total_off_duty_minutes'] / 60,
        })
        
        return totals
