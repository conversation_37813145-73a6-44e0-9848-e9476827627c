from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

app_name = 'trips'

# Create router for ViewSets
router = DefaultRouter()
router.register(r'trips', views.TripViewSet, basename='trip')
router.register(r'eld-logs', views.ELDLogViewSet, basename='eld-log')

urlpatterns = [
    # ViewSet routes
    path('', include(router.urls)),
    
    # Routing and computation endpoints
    path('compute-route/', views.compute_route, name='compute_route'),
    path('geocode/', views.geocode, name='geocode'),
    path('providers/', views.providers, name='providers'),
    
    # Utility endpoints
    path('health/', views.health_check, name='health_check'),
    path('export/', views.export_data, name='export_data'),
]
