import uuid
from django.db import models
from django.contrib.auth import get_user_model
from accounts.models import Vehicle

# Use JSONField from django.db.models (Django 3.1+)
from django.db.models import J<PERSON>NField

User = get_user_model()


class Trip(models.Model):
    """
    Trip model representing a complete trip with multiple waypoints.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('planned', 'Planned'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name='trips')
    vehicle = models.ForeignKey(
        Vehicle, on_delete=models.SET_NULL, null=True, blank=True, related_name='trips')
    name = models.CharField(max_length=255)
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='draft')
    start_time = models.DateTimeField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True)
    total_distance_miles = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True)
    total_duration_minutes = models.IntegerField(null=True, blank=True)
    meta = JSONField(default=dict, blank=True)  # For additional trip metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'trips'
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['start_time']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.name} - {self.user.name}"


class TripWaypoint(models.Model):
    """
    Waypoint model for trip stops with ordered sequence.
    """
    WAYPOINT_TYPES = [
        ('pickup', 'Pickup'),
        ('dropoff', 'Dropoff'),
        ('fuel', 'Fuel Stop'),
        ('rest', 'Rest Stop'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    trip = models.ForeignKey(
        Trip, on_delete=models.CASCADE, related_name='waypoints')
    sequence_order = models.IntegerField()
    waypoint_type = models.CharField(max_length=20, choices=WAYPOINT_TYPES)
    address = models.TextField()
    latitude = models.DecimalField(max_digits=10, decimal_places=7)
    longitude = models.DecimalField(max_digits=10, decimal_places=7)
    scheduled_arrival = models.DateTimeField(null=True, blank=True)
    actual_arrival = models.DateTimeField(null=True, blank=True)
    scheduled_departure = models.DateTimeField(null=True, blank=True)
    actual_departure = models.DateTimeField(null=True, blank=True)
    duration_minutes = models.IntegerField(
        default=60)  # Default 1 hour for pickup/dropoff
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'trip_waypoints'
        indexes = [
            models.Index(fields=['trip', 'sequence_order']),
            models.Index(fields=['waypoint_type']),
            models.Index(fields=['scheduled_arrival']),
        ]
        unique_together = ['trip', 'sequence_order']

    def __str__(self):
        return f"{self.trip.name} - {self.waypoint_type} #{self.sequence_order}"


class ELDLog(models.Model):
    """
    ELD Log model for storing daily duty status logs.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name='eld_logs')
    trip = models.ForeignKey(
        Trip, on_delete=models.CASCADE, related_name='eld_logs', null=True, blank=True)
    log_date = models.DateField()
    entries = JSONField(default=list)  # Array of duty status entries
    total_drive_time_minutes = models.IntegerField(default=0)
    total_on_duty_time_minutes = models.IntegerField(default=0)
    total_off_duty_time_minutes = models.IntegerField(default=0)
    violations = JSONField(default=list)  # Array of compliance violations
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'eld_logs'
        indexes = [
            models.Index(fields=['user', 'log_date']),
            models.Index(fields=['log_date']),
            models.Index(fields=['trip']),
        ]
        unique_together = ['user', 'log_date']

    def __str__(self):
        return f"{self.user.name} - {self.log_date}"


class LogEntry(models.Model):
    """
    Individual log entry for duty status changes.
    """
    DUTY_STATUS_CHOICES = [
        ('off_duty', 'Off Duty'),
        ('sleeper_berth', 'Sleeper Berth'),
        ('driving', 'Driving'),
        ('on_duty_not_driving', 'On Duty Not Driving'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    eld_log = models.ForeignKey(
        ELDLog, on_delete=models.CASCADE, related_name='log_entries')
    duty_status = models.CharField(max_length=20, choices=DUTY_STATUS_CHOICES)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField(null=True, blank=True)
    duration_minutes = models.IntegerField(null=True, blank=True)
    location = models.CharField(max_length=255, blank=True)
    latitude = models.DecimalField(
        max_digits=10, decimal_places=7, null=True, blank=True)
    longitude = models.DecimalField(
        max_digits=10, decimal_places=7, null=True, blank=True)
    odometer_reading = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'log_entries'
        indexes = [
            models.Index(fields=['eld_log', 'start_time']),
            models.Index(fields=['duty_status']),
            models.Index(fields=['start_time']),
        ]

    def __str__(self):
        return f"{self.eld_log.user.name} - {self.duty_status} at {self.start_time}"
