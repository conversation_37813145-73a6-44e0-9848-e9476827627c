# Generated by Django 4.2.7 on 2025-09-27 12:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ELDLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('log_date', models.DateField()),
                ('entries', models.J<PERSON>NField(default=list)),
                ('total_drive_time_minutes', models.IntegerField(default=0)),
                ('total_on_duty_time_minutes', models.IntegerField(default=0)),
                ('total_off_duty_time_minutes', models.IntegerField(default=0)),
                ('violations', models.J<PERSON><PERSON>ield(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'eld_logs',
            },
        ),
        migrations.CreateModel(
            name='Trip',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('planned', 'Planned'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('start_time', models.DateTimeField(blank=True, null=True)),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('total_distance_miles', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('total_duration_minutes', models.IntegerField(blank=True, null=True)),
                ('meta', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trips', to=settings.AUTH_USER_MODEL)),
                ('vehicle', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='trips', to='accounts.vehicle')),
            ],
            options={
                'db_table': 'trips',
            },
        ),
        migrations.CreateModel(
            name='LogEntry',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('duty_status', models.CharField(choices=[('off_duty', 'Off Duty'), ('sleeper_berth', 'Sleeper Berth'), ('driving', 'Driving'), ('on_duty_not_driving', 'On Duty Not Driving')], max_length=20)),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('duration_minutes', models.IntegerField(blank=True, null=True)),
                ('location', models.CharField(blank=True, max_length=255)),
                ('latitude', models.DecimalField(blank=True, decimal_places=7, max_digits=10, null=True)),
                ('longitude', models.DecimalField(blank=True, decimal_places=7, max_digits=10, null=True)),
                ('odometer_reading', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('eld_log', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='log_entries', to='trips.eldlog')),
            ],
            options={
                'db_table': 'log_entries',
            },
        ),
        migrations.AddField(
            model_name='eldlog',
            name='trip',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='eld_logs', to='trips.trip'),
        ),
        migrations.AddField(
            model_name='eldlog',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='eld_logs', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='TripWaypoint',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('sequence_order', models.IntegerField()),
                ('waypoint_type', models.CharField(choices=[('pickup', 'Pickup'), ('dropoff', 'Dropoff'), ('fuel', 'Fuel Stop'), ('rest', 'Rest Stop')], max_length=20)),
                ('address', models.TextField()),
                ('latitude', models.DecimalField(decimal_places=7, max_digits=10)),
                ('longitude', models.DecimalField(decimal_places=7, max_digits=10)),
                ('scheduled_arrival', models.DateTimeField(blank=True, null=True)),
                ('actual_arrival', models.DateTimeField(blank=True, null=True)),
                ('scheduled_departure', models.DateTimeField(blank=True, null=True)),
                ('actual_departure', models.DateTimeField(blank=True, null=True)),
                ('duration_minutes', models.IntegerField(default=60)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('trip', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='waypoints', to='trips.trip')),
            ],
            options={
                'db_table': 'trip_waypoints',
                'indexes': [models.Index(fields=['trip', 'sequence_order'], name='trip_waypoi_trip_id_06b73f_idx'), models.Index(fields=['waypoint_type'], name='trip_waypoi_waypoin_485dea_idx'), models.Index(fields=['scheduled_arrival'], name='trip_waypoi_schedul_155846_idx')],
                'unique_together': {('trip', 'sequence_order')},
            },
        ),
        migrations.AddIndex(
            model_name='trip',
            index=models.Index(fields=['user', 'status'], name='trips_user_id_0bc744_idx'),
        ),
        migrations.AddIndex(
            model_name='trip',
            index=models.Index(fields=['start_time'], name='trips_start_t_005982_idx'),
        ),
        migrations.AddIndex(
            model_name='trip',
            index=models.Index(fields=['status'], name='trips_status_b30e85_idx'),
        ),
        migrations.AddIndex(
            model_name='logentry',
            index=models.Index(fields=['eld_log', 'start_time'], name='log_entries_eld_log_fc464c_idx'),
        ),
        migrations.AddIndex(
            model_name='logentry',
            index=models.Index(fields=['duty_status'], name='log_entries_duty_st_9ff118_idx'),
        ),
        migrations.AddIndex(
            model_name='logentry',
            index=models.Index(fields=['start_time'], name='log_entries_start_t_4096b6_idx'),
        ),
        migrations.AddIndex(
            model_name='eldlog',
            index=models.Index(fields=['user', 'log_date'], name='eld_logs_user_id_ded7de_idx'),
        ),
        migrations.AddIndex(
            model_name='eldlog',
            index=models.Index(fields=['log_date'], name='eld_logs_log_dat_5adb55_idx'),
        ),
        migrations.AddIndex(
            model_name='eldlog',
            index=models.Index(fields=['trip'], name='eld_logs_trip_id_587bbf_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='eldlog',
            unique_together={('user', 'log_date')},
        ),
    ]
