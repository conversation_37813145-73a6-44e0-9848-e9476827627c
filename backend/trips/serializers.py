from rest_framework import serializers
from .models import Trip, TripWaypoint, ELDLog, LogEntry
from accounts.serializers import UserSerializer, VehicleSerializer


class TripWaypointSerializer(serializers.ModelSerializer):
    """
    Serializer for trip waypoints.
    """
    class Meta:
        model = TripWaypoint
        fields = (
            'id', 'sequence_order', 'waypoint_type', 'address', 
            'latitude', 'longitude', 'scheduled_arrival', 'actual_arrival',
            'scheduled_departure', 'actual_departure', 'duration_minutes',
            'created_at', 'updated_at'
        )
        read_only_fields = ('id', 'created_at', 'updated_at')


class TripSerializer(serializers.ModelSerializer):
    """
    Serializer for trips with nested waypoints.
    """
    waypoints = TripWaypointSerializer(many=True, read_only=True)
    user = UserSerializer(read_only=True)
    vehicle = VehicleSerializer(read_only=True)
    vehicle_id = serializers.UUIDField(write_only=True, required=False, allow_null=True)

    class Meta:
        model = Trip
        fields = (
            'id', 'name', 'status', 'start_time', 'end_time',
            'total_distance_miles', 'total_duration_minutes', 'meta',
            'user', 'vehicle', 'vehicle_id', 'waypoints',
            'created_at', 'updated_at'
        )
        read_only_fields = ('id', 'user', 'created_at', 'updated_at')

    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class TripCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating trips with waypoints.
    """
    waypoints = TripWaypointSerializer(many=True)
    vehicle_id = serializers.UUIDField(required=False, allow_null=True)

    class Meta:
        model = Trip
        fields = (
            'name', 'status', 'start_time', 'end_time',
            'vehicle_id', 'waypoints', 'meta'
        )

    def create(self, validated_data):
        waypoints_data = validated_data.pop('waypoints', [])
        validated_data['user'] = self.context['request'].user
        
        trip = Trip.objects.create(**validated_data)
        
        for waypoint_data in waypoints_data:
            TripWaypoint.objects.create(trip=trip, **waypoint_data)
        
        return trip


class LogEntrySerializer(serializers.ModelSerializer):
    """
    Serializer for individual log entries.
    """
    class Meta:
        model = LogEntry
        fields = (
            'id', 'duty_status', 'start_time', 'end_time', 'duration_minutes',
            'location', 'latitude', 'longitude', 'odometer_reading', 'notes',
            'created_at', 'updated_at'
        )
        read_only_fields = ('id', 'created_at', 'updated_at')


class ELDLogSerializer(serializers.ModelSerializer):
    """
    Serializer for ELD logs with nested log entries.
    """
    log_entries = LogEntrySerializer(many=True, read_only=True)
    user = UserSerializer(read_only=True)
    trip = TripSerializer(read_only=True)

    class Meta:
        model = ELDLog
        fields = (
            'id', 'log_date', 'entries', 'total_drive_time_minutes',
            'total_on_duty_time_minutes', 'total_off_duty_time_minutes',
            'violations', 'user', 'trip', 'log_entries',
            'created_at', 'updated_at'
        )
        read_only_fields = ('id', 'user', 'created_at', 'updated_at')


class TripListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for trip lists.
    """
    user = UserSerializer(read_only=True)
    vehicle = VehicleSerializer(read_only=True)
    waypoint_count = serializers.SerializerMethodField()

    class Meta:
        model = Trip
        fields = (
            'id', 'name', 'status', 'start_time', 'end_time',
            'total_distance_miles', 'total_duration_minutes',
            'user', 'vehicle', 'waypoint_count', 'created_at', 'updated_at'
        )

    def get_waypoint_count(self, obj):
        return obj.waypoints.count()


class RouteComputationSerializer(serializers.Serializer):
    """
    Serializer for route computation requests.
    """
    waypoints = serializers.ListField(
        child=serializers.DictField(),
        help_text="List of waypoint objects with address, latitude, longitude"
    )
    start_time = serializers.DateTimeField(required=False)
    vehicle_id = serializers.UUIDField(required=False, allow_null=True)
    
    def validate_waypoints(self, value):
        if len(value) < 2:
            raise serializers.ValidationError("At least 2 waypoints are required")
        
        for waypoint in value:
            required_fields = ['address', 'latitude', 'longitude']
            for field in required_fields:
                if field not in waypoint:
                    raise serializers.ValidationError(f"Waypoint missing required field: {field}")
        
        return value


class GeocodeSerializer(serializers.Serializer):
    """
    Serializer for geocoding requests.
    """
    address = serializers.CharField(max_length=500)
    
    
class ExportSerializer(serializers.Serializer):
    """
    Serializer for export requests.
    """
    format = serializers.ChoiceField(choices=['csv', 'pdf'], default='csv')
    start_date = serializers.DateField(required=False)
    end_date = serializers.DateField(required=False)
    trip_ids = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        help_text="List of trip IDs to export"
    )
