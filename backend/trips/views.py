from rest_framework import generics, status, viewsets
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.permissions import IsA<PERSON>enticated, AllowAny
from rest_framework.response import Response
from django.utils import timezone
from django.http import HttpResponse
from django.db.models import Q
from datetime import datetime, timedelta
import csv
import json

from .models import Trip, TripWaypoint, ELDLog, LogEntry
from .serializers import (
    TripSerializer, TripCreateSerializer, TripListSerializer,
    TripWaypointSerializer, ELDLogSerializer, LogEntrySerializer,
    RouteComputationSerializer, GeocodeSerializer, ExportSerializer
)
from .eld_calculator import ELDComplianceCalculator
from routing.services import RoutingService


class TripViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing trips with ELD compliance.
    """
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'create':
            return TripCreateSerializer
        elif self.action == 'list':
            return TripListSerializer
        return TripSerializer

    def get_queryset(self):
        queryset = Trip.objects.filter(
            user=self.request.user).order_by('-created_at')

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date:
            queryset = queryset.filter(start_time__gte=start_date)
        if end_date:
            queryset = queryset.filter(start_time__lte=end_date)

        return queryset.prefetch_related('waypoints', 'vehicle')

    @action(detail=True, methods=['post'])
    def calculate_compliance(self, request, pk=None):
        """
        Calculate ELD compliance for a specific trip.
        """
        trip = self.get_object()
        calculator = ELDComplianceCalculator()

        # Prepare trip data for compliance calculation
        trip_data = {
            'waypoints': list(trip.waypoints.values(
                'sequence_order', 'waypoint_type', 'address',
                'latitude', 'longitude', 'duration_minutes'
            ).order_by('sequence_order')),
            'start_time': trip.start_time,
            'end_time': trip.end_time
        }

        compliance_result = calculator.calculate_trip_compliance(trip_data)

        return Response(compliance_result)

    @action(detail=True, methods=['post'])
    def compute_route(self, request, pk=None):
        """
        Compute route for trip waypoints.
        """
        trip = self.get_object()
        routing_service = RoutingService()

        waypoints = list(trip.waypoints.values(
            'latitude', 'longitude', 'address', 'waypoint_type'
        ).order_by('sequence_order'))

        if len(waypoints) < 2:
            return Response(
                {'error': 'At least 2 waypoints required for route computation'},
                status=status.HTTP_400_BAD_REQUEST
            )

        provider = request.data.get('provider', 'osrm')
        route_result = routing_service.compute_route(waypoints, provider)

        # Update trip with route information
        trip.total_distance_miles = route_result['distance_miles']
        trip.total_duration_minutes = route_result['duration_minutes']
        trip.save()

        return Response(route_result)

    @action(detail=False, methods=['post'])
    def bulk_delete(self, request):
        """
        Bulk delete trips by IDs.
        """
        trip_ids = request.data.get('trip_ids', [])
        if not trip_ids:
            return Response(
                {'error': 'trip_ids required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        deleted_count = Trip.objects.filter(
            id__in=trip_ids,
            user=request.user
        ).delete()[0]

        return Response({'deleted_count': deleted_count})


class ELDLogViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing ELD logs.
    """
    serializer_class = ELDLogSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = ELDLog.objects.filter(
            user=self.request.user).order_by('-log_date')

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date:
            queryset = queryset.filter(log_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(log_date__lte=end_date)

        return queryset.prefetch_related('log_entries', 'trip')

    @action(detail=False, methods=['get'])
    def cycle_compliance(self, request):
        """
        Get 70-hour 8-day cycle compliance for current user.
        """
        calculator = ELDComplianceCalculator()
        date = timezone.now()

        compliance_result = calculator.calculate_cycle_compliance(
            str(request.user.id),
            date
        )

        return Response(compliance_result)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def compute_route(request):
    """
    Compute route for given waypoints.
    """
    serializer = RouteComputationSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    waypoints = serializer.validated_data['waypoints']
    provider = request.data.get('provider', 'osrm')

    routing_service = RoutingService()
    calculator = ELDComplianceCalculator()

    # Compute basic route
    route_result = routing_service.compute_route(waypoints, provider)

    # Insert fuel stops if needed
    total_distance = route_result['distance_miles']
    waypoints_with_fuel = calculator.insert_fuel_stops(
        waypoints, total_distance)

    # Recompute route with fuel stops if they were added
    if len(waypoints_with_fuel) > len(waypoints):
        route_result = routing_service.compute_route(
            waypoints_with_fuel, provider)
        route_result['fuel_stops_added'] = len(
            waypoints_with_fuel) - len(waypoints)
    else:
        route_result['fuel_stops_added'] = 0

    # Calculate ELD compliance
    if serializer.validated_data.get('start_time'):
        trip_data = {
            'waypoints': waypoints_with_fuel,
            'start_time': serializer.validated_data['start_time']
        }
        compliance_result = calculator.calculate_trip_compliance(trip_data)
        route_result['eld_compliance'] = compliance_result

    return Response(route_result)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def geocode(request):
    """
    Geocode an address to get coordinates.
    """
    serializer = GeocodeSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    address = serializer.validated_data['address']
    routing_service = RoutingService()

    result = routing_service.geocode_address(address)

    if result:
        return Response(result)
    else:
        return Response(
            {'error': 'Address not found'},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def providers(request):
    """
    Get list of available mapping providers.
    """
    routing_service = RoutingService()
    providers_list = routing_service.get_available_providers()

    return Response({'providers': providers_list})


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """
    Health check endpoint for monitoring.
    """
    return Response({
        'status': 'healthy',
        'timestamp': timezone.now().isoformat(),
        'version': '1.0.0'
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def export_data(request):
    """
    Export trip and ELD data in various formats.
    """
    serializer = ExportSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    export_format = serializer.validated_data['format']
    start_date = serializer.validated_data.get('start_date')
    end_date = serializer.validated_data.get('end_date')
    trip_ids = serializer.validated_data.get('trip_ids')

    # Build queryset
    queryset = Trip.objects.filter(user=request.user)

    if trip_ids:
        queryset = queryset.filter(id__in=trip_ids)
    if start_date:
        queryset = queryset.filter(start_time__gte=start_date)
    if end_date:
        queryset = queryset.filter(start_time__lte=end_date)

    trips = queryset.prefetch_related(
        'waypoints', 'eld_logs').order_by('start_time')

    if export_format == 'csv':
        return _export_csv(trips, request.user)
    elif export_format == 'pdf':
        return Response(
            {'error': 'PDF export not implemented yet'},
            status=status.HTTP_501_NOT_IMPLEMENTED
        )

    return Response(
        {'error': 'Invalid export format'},
        status=status.HTTP_400_BAD_REQUEST
    )


def _export_csv(trips, user):
    """
    Export trips data as CSV.
    """
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="trips_export_{timezone.now().strftime("%Y%m%d")}.csv"'

    writer = csv.writer(response)

    # Write header
    writer.writerow([
        'Trip Name', 'Status', 'Start Time', 'End Time',
        'Total Distance (miles)', 'Total Duration (minutes)',
        'Vehicle', 'Waypoint Count', 'Created At'
    ])

    # Write trip data
    for trip in trips:
        writer.writerow([
            trip.name,
            trip.get_status_display(),
            trip.start_time.isoformat() if trip.start_time else '',
            trip.end_time.isoformat() if trip.end_time else '',
            trip.total_distance_miles or '',
            trip.total_duration_minutes or '',
            trip.vehicle.name if trip.vehicle else '',
            trip.waypoints.count(),
            trip.created_at.isoformat()
        ])

    return response
