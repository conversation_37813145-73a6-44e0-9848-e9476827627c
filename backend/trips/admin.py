from django.contrib import admin
from .models import Trip, TripWaypoint, ELDLog, LogEntry


class TripWaypointInline(admin.TabularInline):
    model = TripWaypoint
    extra = 0
    ordering = ('sequence_order',)


@admin.register(Trip)
class TripAdmin(admin.ModelAdmin):
    list_display = ('name', 'user', 'status', 'start_time',
                    'total_distance_miles', 'created_at')
    list_filter = ('status', 'created_at', 'start_time')
    search_fields = ('name', 'user__name', 'user__email')
    ordering = ('-created_at',)
    inlines = [TripWaypointInline]


@admin.register(TripWaypoint)
class TripWaypointAdmin(admin.ModelAdmin):
    list_display = ('trip', 'sequence_order', 'waypoint_type',
                    'address', 'scheduled_arrival')
    list_filter = ('waypoint_type', 'created_at')
    search_fields = ('trip__name', 'address')
    ordering = ('trip', 'sequence_order')


class LogEntryInline(admin.TabularInline):
    model = LogEntry
    extra = 0
    ordering = ('start_time',)


@admin.register(ELDLog)
class ELDLogAdmin(admin.ModelAdmin):
    list_display = ('user', 'log_date', 'total_drive_time_minutes',
                    'total_on_duty_time_minutes', 'trip')
    list_filter = ('log_date', 'created_at')
    search_fields = ('user__name', 'user__email', 'trip__name')
    ordering = ('-log_date',)
    inlines = [LogEntryInline]


@admin.register(LogEntry)
class LogEntryAdmin(admin.ModelAdmin):
    list_display = ('eld_log', 'duty_status', 'start_time',
                    'end_time', 'duration_minutes', 'location')
    list_filter = ('duty_status', 'start_time')
    search_fields = ('eld_log__user__name', 'location')
    ordering = ('-start_time',)
