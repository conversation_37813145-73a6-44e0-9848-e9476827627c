Full Plan — Build a Full-Stack ELD Trip Planning App

Stack: Django (Django REST Framework) backend + React frontend (Vite)
Hosting: Frontend → Vercel, Backend → Render (managed Postgres)
Deliverables checklist (for assessment):

Live hosted app (Vercel + Render) with working demo link

Public GitHub repo with complete source, clear README & deployment steps

3–5 minute Loom video demonstrating app & brief code walkthrough

$150 reward upon verification (include hosted link + GitHub + Loom)

Below is a step-by-step, actionable development plan covering architecture, schema, API strategy, ELD logic, UI, endpoints, deployment, QA, timeline, risks and mitigations.

1) High-level architecture & technology decisions

Why these choices

Django + DRF — mature, batteries-included for auth, migrations, Postgres; easy to implement timezone-aware timestamps and server-side validation for ELD rules.

Postgres — reliable relational data (trip logs, duty segments), supports geospatial (PostGIS optional).

React (Vite) + Tailwind CSS — fast developer experience, modern UI, easy to make professional designs that meet the quality requirement.

Map tech — Mapbox GL JS (feature rich) OR Leaflet + OpenStreetMap tiles + OSRM/OpenRouteService for routing; implement a strategy that allows switching providers to avoid single-provider lock.

Auth & Security — JWT (Simple JWT) for API auth; HTTPS enforced by hosting.

Local dev — Docker Compose with services: web (Django), db (Postgres), frontend (optional), and a mock routing service if needed.

CI/CD — GitHub Actions to run tests and deploy: push to main triggers Render for backend; Vercel auto-deploy for frontend.

Extras

Use Sentry for error reporting (optional).

Use Cloudinary or S3 for any uploaded assets (not strictly needed).

Use WhiteNoise for Django static files or serve static from CDN.

2) Database schema design (core tables)

Use UUID primary keys, timestamps (created_at, updated_at). Minimal necessary fields shown.

Tables
users

id: UUID PK

email: string (unique)

name: string

password_hash

role (driver/admin)

created_at, updated_at

vehicles (optional)

id: UUID

name, unit_number, odometer_start (number)

created_at, updated_at

trips

id: UUID

user_id: FK -> users

vehicle_id: FK -> vehicles (optional)

name

start_datetime (timezone aware)

end_datetime (estimated)

total_miles_estimate

current_cycle_hours_used (hours numeric) — initial input

status (planned, active, completed, canceled)

meta: JSONB (store provider/routing metadata)

created_at, updated_at

trip_waypoints (ordered)

id: UUID

trip_id: FK

order_index: int

type: enum {pickup, dropoff, fuel, rest, mandatory_stop, intermediate}

address: string

lat, lon: decimal

scheduled_arrival, scheduled_departure (datetime)

duration_minutes (e.g., 60 for pickup/dropoff)

miles_from_prev, time_from_prev (computed)

created_at, updated_at

eld_logs (per trip per day)

id: UUID

trip_id: FK

date (date)

driver_id: FK

cycle_hours_before (numeric)

cycle_hours_after (numeric)

total_driving_hours (for day)

total_on_duty_hours

entries: JSONB — array of log entries/duty status segments

render_html (cached HTML/PDF markup of sheet)

created_at, updated_at

log_entries (normalized duty segments) — optional detailed table

id: UUID

eld_log_id: FK

start_time, end_time

duty_status (driving, on_duty_not_driving, off_duty, sleeper)

start_lat, start_lon, end_lat, end_lon

miles

notes

created_at, updated_at

Indexes

Trips by user and status

Waypoints by trip and order_index

eld_logs by trip + date

Notes: Storing both normalized entries and an aggregated JSONB entries is helpful — JSON for fast rendering and normalized rows for query/analytics.

3) Mapping & routing API integration strategy

Goal: interactive route on map, multi-day routing, insert mandatory fuel & rest stops.

Providers (choose one primary + fallback):

Primary: Mapbox Directions + Mapbox Geocoding + Mapbox Tiles

Pros: smooth integration with Mapbox GL JS, good routing features.

Cons: API key/rate limits.

Fallback / Open option: OSRM (hosted or public) or OpenRouteService + Nominatim geocoding + OSM tiles (via Leaflet).

Pros: truly free, avoid billing.

Cons: slightly more configuration and potential hosting cost for OSRM if self-hosted.

Integration approach

Backend proxy endpoints (recommended) to mediate between frontend and mapping providers:

POST /api/routing/compute takes waypoints; server calls Mapbox/OSRM, applies business rules server-side (insert stops), returns route geometry + turn-by-turn + segment durations.

GET /api/geocode?address=... server proxies to Mapbox or Nominatim to hide keys and cache results.

Client uses Mapbox GL or Leaflet to render returned route GeoJSON and stops.

Caching: cache route results and geocoding responses for repeated queries (Redis or DB table) to reduce API usage and speed UX.

Rate limiting & retries: implement exponential backoff for provider errors and graceful UI fallback: “Routing temporarily unavailable — try again”.

Routing specifics for business rules

Compute total distance -> insert fuel stops every 1,000 miles (or at closest suitable POIs along the route). Use route geometry to find candidate fuel-stop coordinates.

Insert 1-hour stops at pickup and dropoff waypoints (automatically added).

Segment route into daily chunks based on available driving hours and cycle hours left (see next section).

4) ELD compliance calculations & log generation logic

This is core. I’ll deliver precise, testable rules and sample pseudocode logic (no code), and acceptance criteria.

Business rules & assumptions (explicit)

Cycle limit: 70 hours per 8-day cycle (property-carrying).

Daily driving window assumption (recommended): follow typical property-carrying: up to 11 hours driving after 10 consecutive hours off-duty; 14-hour on-duty window. (Note: include as configurable settings so test harness can adapt.)

Pickup/Dropoff: 1 hour each (non-driving on-duty time unless user marks otherwise).

Fuel: mandatory stop every 1,000 miles (insert stops as waypoints).

No adverse conditions.

All times are timezone aware.

Calculation steps (server side):

Inputs: start_location, pickup, dropoff, current_cycle_hours_used, trip start datetime (or now).

Get route from routing provider: total distance (miles), segment distances, travel times.

Insert scheduled stops:

Add pickup & dropoff stops with fixed 60 minutes dwell.

Walk route geometry and insert fuel waypoints every ≤1000 miles (choose nearest road POI or coordinate).

Segment into driving blocks by day:

Maintain remaining_cycle_hours = 70 - current_cycle_hours_used.

For each day:

Allowed driving today = min(11 hours, remaining_cycle_hours) — (configurable).

Consider on-duty time (driving + pickup/dropoff durations + fueling durations + breaks).

Create driving segments until either route finished or allowed driving exhausted.

Insert rest block (off-duty) when required (e.g., after reaching driving limit or end of day).

Update remaining_cycle_hours by subtracting driving hours used in that day.

Continue next day starting with 10 hours off-duty assumed between driving shifts (or as per configuration).

Generate ELD entries per day:

Build ordered log_entries with start/end times, duty status (driving or on-duty not driving for pickups/fuel), lat/lon snapshots at segment boundaries.

Fill eld_logs rows for each date spanning the trip; multiple daily sheets for multi-day trip.

Validation rules (must pass):

No day exceeds allowed driving hours.

Cumulative driving hours across an 8-day rolling window ≤ 70.

Pickup/dropoff durations counted as on-duty not driving.

Edge & override rules:

Allow manual overrides in UI (e.g., administrative correction) — log changes and reason (audit trail).

If remaining cycle_hours < needed to finish route, warn user and schedule mandatory off-duty time before continuing.

Outputs

For each day: eld_log with:

date, start_time, end_time

entries: array of {start, end, status, lat/lon, miles}

daily totals (driving, on-duty, off-duty)

Route geometry with waypoints and per-segment metadata to render on map.

Acceptance criteria

Generated logs match business rules above for sample scenarios (unit tests cover edge cases such as crossing midnight, multiple fuel stops, cycle exhaustion mid-route).

5) Frontend component structure & user flow

Design for professional UI/UX using Tailwind + component library (shadcn or Chakra/Material optional). Responsive and printable ELD sheets.

Pages & main components

Auth / Landing

Login, Register (optional demo mode)

Dashboard

Quick summary: active trips, cycle hours remaining, recent logs

Plan Trip (core)

Components:

TripForm — inputs: current location, pickup, dropoff, start datetime, current_cycle_hours_used

MapCanvas — interactive map (Mapbox/Leaflet) to display route

WaypointsList — editable list of stops (reorderable)

RoutingOptionsPanel — provider selector, fuel interval, driving limits

ComputeButton — calls backend /api/routing/compute

Trip Details

RouteOverview — distance, est duration, fuel stops

DailyLogSheets — render multiple ELDLogSheet components, with print/export (PDF) button

Timeline — draggable timeline to tweak duty segments (UI to adjust start/end times)

ManualAdjustModal — change times, duty status, add notes (audit logged)

Logs / Archive

List of completed trips & logs, filters by date/user

Settings

HOS rules (11/14/70 configurable), map provider keys, vehicle data

Help / About

Compliance notes + README link

UI/UX considerations

Use clear visual duty status colors on timeline & ELD sheets.

Provide print-friendly ELD sheet (A4) that matches accepted format.

Mobile friendly: Map priority, quick plan flow.

Accessibility (contrast, keyboard nav) — run axe checks.

User flow (Plan Trip)

Enter current location (auto via browser geolocation) OR address. Enter pickup & dropoff.

Enter current cycle hours used.

Click Plan Trip -> show loading + progress.

Backend returns route + ELD logs. Show map with route, stops (fuel/rest), daily segmentation in side panel.

Ability to fine-tune (drag stops, change start time). Recompute logs as needed.

Save trip -> trip list & generate shareable link + ability to print logs and export JSON/PDF.

6) Backend API endpoints & data processing

Design APIs primarily RESTful; important endpoints listed with expected inputs/outputs.

Auth

POST /api/auth/login — {email, password} -> {access_token, user}

POST /api/auth/register — optional

Trips

POST /api/trips/ — create plan (inputs: start_location, pickup, dropoff, start_datetime, current_cycle_hours_used, options) -> returns trip_id, preview data

GET /api/trips/ — list user trips

GET /api/trips/{id} — full trip details (route geojson + logs)

PATCH /api/trips/{id} — edit trip (waypoints, times)

DELETE /api/trips/{id}

Routing & ELD computation

POST /api/routing/compute — body: {waypoints: [{lat,lon,type}], start_datetime, current_cycle_hours_used, options}

Server steps:

Geocode addresses if only text provided.

Call routing provider for route geometry & distances.

Insert fuel & mandatory stops by business rules.

Segment into daily driving blocks using ELD logic.

Return: {route_geojson, segments: [...], daily_eld_logs: [...], warnings: [...]}

POST /api/routing/recompute — accepts manual adjustments and recomputes.

Geocoding (proxy)

GET /api/geocode?address=... — returns coords (caching applied)

Utility

GET /api/providers — list mapping providers supported

GET /api/health — service health

GET /api/export/{trip_id}?format=pdf|json — export logs

Data processing considerations

Keep heavy routing calls asynchronous but respond synchronously for this assessment — or provide progress polling endpoint if route computation may take longer.

Log all input and outputs for audit & debugging (do not store API keys in logs).

7) Deployment strategy (frontend + backend)

Backend (Render)

Create a GitHub repo and connect to Render service.

Use Render web service (Docker or Python environment) with environment variables:

DATABASE_URL (managed Postgres)

DJANGO_SECRET_KEY, MAPBOX_TOKEN (or other provider keys)

ALLOWED_HOSTS, SENTRY_DSN (if configured)

Run migrations automatically on deploy (use Render deploy hook script).

Use gunicorn + whitenoise (or run with Django+gunicorn). For websockets (if used), Render supports web sockets.

Use managed Postgres on Render or separate provider. Enable daily backups.

Frontend (Vercel)

Connect GitHub repo to Vercel; set environment variables: VITE_API_BASE, VITE_MAPBOX_TOKEN, etc.

Automatic deploys on push to main. Use preview branches for staging.

Ensure CORS on backend allows Vercel domain.

CI/CD

GitHub Actions:

Run unit tests (backend & frontend).

Linting (flake8, eslint).

On success, push to main triggers Render/Vercel via their GitHub integrations.

Produce artifacts (test results).

Domains & SSL

Use Vercel’s managed domain for frontend + automatic HTTPS.

Render provides managed certificates.

Monitoring & Logging

Integrate Sentry, and use Render logs for backend.

8) Testing approach & quality assurance

Testing pyramid

Unit tests (Django + Jest for React): ELD calculations, route segmentation, DB model validations, React components snapshot.

Integration tests: API endpoint behavior (DRF tests), route compute end-to-end with mocked mapping provider.

End-to-end (E2E): Cypress or Playwright to test main flows (Plan Trip -> View Logs -> Print).

Accessibility tests: axe-core, Lighthouse audits.

Visual regression: Percy or Chromatic for critical UI components (optional).

Performance & Load: Locust for backend routing endpoints (simulate multiple route computes).

Security checks: dependency scans (GitHub Dependabot), basic OWASP checks.

Test data

Create test fixtures for common trip types: short trip (<1 day), multi-day trip (2–4 days), route that requires multiple fuel stops, cycle exhaustion mid-route.

Acceptance criteria

All unit tests pass.

CI pipeline green on each PR.

Hosted app passes smoke tests: plan trip, display route, generate at least one multi-day log sheet, print preview.

9. Potential challenges & mitigation strategies

Routing provider rate limits / costs

Mitigation: use caching, set sensible retry/backoff, prefer OSRM/OpenRouteService for heavy load or run your own OSRM instance for production.

Complexity of HOS regulations

Mitigation: make HOS rules configurable in Settings; keep algorithm modular and well-tested; document assumptions prominently.

Edge cases (mid-night segments, DST, crossing timezones)

Mitigation: store all times in UTC + display in local timezone; write unit tests for DST and midnight boundary cases.

Map accuracy for inserted fuel stops

Mitigation: when inserting fuel stops, pick nearest road coordinate and provide UI to manually move/accept suggested fuel stop.

UI/UX polish required by assessment

Mitigation: invest time in Tailwind-based components, consistent spacing, user testing with a checklist: clarity, print view, color contrast. Use a simple design system.

PDF/print rendering of ELD sheets

Mitigation: design a dedicated print stylesheet, test in headless browser and on different viewports. Use server-side HTML->PDF (wkhtmltopdf) only if needed.

Deployment differences between local & hosting

Mitigation: use Docker locally to match hosting; document environment variables and ensure migrations auto-run in Render deploy hook.

Time to create Loom video + documentation

Mitigation: script the 3–5 minute demo before recording — show hosted app, generate a trip, show ELD sheets, then a quick tour of code paths (where ELD algorithm lives, routing service, frontend components).

Final checklist for assessment submission

Before asking for reward verification, ensure the following are included in the GitHub repo and deployed app:

 Public GitHub repo containing backend and frontend directories + monorepo README

 Clear README: how to run locally (Docker), env vars required, architecture diagram, HOS assumptions

 Hosted frontend link (Vercel) and hosted backend link (Render) in README

 Loom video link (3–5 mins) in README — show functionality and 60–90s code walkthrough pointing to key files:

Backend: routing/compute service & ELD algorithm module

Frontend: TripForm, MapCanvas, ELDLogSheet

 Automated tests in CI (unit + a couple integration tests)

 Demo account credentials (or public demo mode) so reviewer can login and test

 Export/printable ELD sheets for a sample multi-day trip.